# SQL
EXTERNAL_PGSQL = False
# PGSQL_HOST = "localhost" // dev
PGSQL_HOST = "***************"
PGSQL_PORT = 5432

PGSQL_DB = "postgres"
PGSQL_USER = "postgres"
PGSQL_PASSWORD = "Password123@vupbi"

PGSQL_CONFIG = {
    "host": PGSQL_HOST,
    "port": PGSQL_PORT,
    "database": PGSQL_DB,
    "user": PGSQL_USER,
    "password": PGSQL_PASSWORD,
    "ssl": False,
    "timeout": 30,
    "command_timeout": 60.0,
    "min_size": 10,
    "max_size": 50,
    "max_inactive_connection_lifetime": 300,
    "max_queries": 50000,
}
