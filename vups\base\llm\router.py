from typing import Optional
from langchain_community.llms.baichuan import BaichuanLLM
from langchain_openai import ChatOpenAI

from vups.logger import logger
from vups.base.llm.config import LLMConfig, default_config


def _check_config(cfg: LLMConfig, model: str) -> bool:
    if model.startswith("hunyuan"):
        return bool(cfg.hunyuan_api_base and cfg.hunyuan_api_key)
    elif model.startswith(("claude", "gpt")):
        return bool(cfg.anthropic_api_base and cfg.anthropic_api_key)
    elif model == "baichuan4":
        return bool(cfg.baichuan_api_base and cfg.baichuan_api_key)
    elif model.endswith(":free") or model == "reflection-70b":
        return bool(cfg.openrouter_api_base and cfg.openrouter_api_key)
    elif model in ["qwen2.5-72b", "qwen3-235b", "yi"]:
        return bool(cfg.ollama_api_base and cfg.ollama_api_key)
    elif model == "llama3.1":
        return bool(cfg.llama_api_base and cfg.llama_api_key)
    return False

# TODO： Rename for things
def get_llm(model="hunyuan", temperature=0, config: Optional[LLMConfig] = None):
    """
    Get a language model instance based on the specified model name and configuration.

    Args:
        model (str): The name of the model to use
        temperature (float): The temperature parameter for text generation
        config (Optional[LLMConfig]): Configuration object containing API settings.
                                    If None, uses default_config

    Returns:
        An instance of the specified language model, or None if required configuration is missing
    """
    # Use provided config or fall back to default
    cfg = config or default_config

    if not _check_config(cfg, model):
        return None

    if model == "hunyuan-standard-256K":
        llm = ChatOpenAI(
            temperature=temperature,
            openai_api_base=cfg.hunyuan_api_base,
            openai_api_key=cfg.hunyuan_api_key,
            model_name="hunyuan-standard-256K",
        )
    elif model == "claude-3-opus":
        llm = ChatOpenAI(
            temperature=temperature,
            openai_api_base=cfg.anthropic_api_base,
            openai_api_key=cfg.anthropic_api_key,
            model_name="claude-3-opus-20240229",
        )
    elif model == "claude-3-5-sonnet":
        llm = ChatOpenAI(
            temperature=temperature,
            openai_api_base=cfg.anthropic_api_base,
            openai_api_key=cfg.anthropic_api_key,
            model_name="claude-3-5-sonnet-20241022",
        )
    elif model == "baichuan4":
        llm = BaichuanLLM(
            temperature=temperature,
            baichuan_api_host=cfg.baichuan_api_base,
            baichuan_api_key=cfg.baichuan_api_key,
            model="Baichuan4",
        )
    elif model == "gpt-3.5-turbo":
        llm = ChatOpenAI(
            temperature=temperature,
            openai_api_base=cfg.anthropic_api_base,
            openai_api_key=cfg.anthropic_api_key,
            model_name="gpt-3.5-turbo",
        )
    elif model == "gpt-4o":
        llm = ChatOpenAI(
            temperature=temperature,
            openai_api_base=cfg.anthropic_api_base,
            openai_api_key=cfg.anthropic_api_key,
            model_name="gpt-4o",
        )
    elif model == "reflection-70b":
        llm = ChatOpenAI(
            temperature=temperature,
            openai_api_base=cfg.openrouter_api_base,
            openai_api_key=cfg.openrouter_api_key,
            model_name="mattshumer/reflection-70b:free",
        )
    elif model == "deepseek-prover-v2:free":
        llm = ChatOpenAI(
            temperature=temperature,
            openai_api_base=cfg.openrouter_api_base,
            openai_api_key=cfg.openrouter_api_key,
            model_name="deepseek/deepseek-prover-v2:free",
        )
    elif model == "qwen3-235b-a22b:free":
        llm = ChatOpenAI(
            temperature=temperature,
            openai_api_base=cfg.openrouter_api_base,
            openai_api_key=cfg.openrouter_api_key,
            model_name="qwen/qwen3-235b-a22b:free",
        )
    elif model == "qwen/qwen3-32b:free":
        llm = ChatOpenAI(
            temperature=temperature,
            openai_api_base=cfg.openrouter_api_base,
            openai_api_key=cfg.openrouter_api_key,
            model_name="qwen/qwen3-235b-a22b:free",
        )
    elif model == "deepseek-chat-v3-0324:free":
        llm = ChatOpenAI(
            temperature=0.6,
            openai_api_base=cfg.openrouter_api_base,
            openai_api_key=cfg.openrouter_api_key,
            model_name="deepseek/deepseek-chat-v3-0324:free",
        )
    elif model == "deepseek-r1:free":
        llm = ChatOpenAI(
            temperature=0.6,
            openai_api_base=cfg.openrouter_api_base,
            openai_api_key=cfg.openrouter_api_key,
            model_name="deepseek/deepseek-r1:free",
        )
    elif model == "qwen2.5-72b":
        llm = ChatOpenAI(
            temperature=temperature,
            openai_api_base=cfg.ollama_api_base,
            openai_api_key=cfg.ollama_api_key,
            model_name="qwen2.5:72b",
        )
    elif model == "qwen3-235b":
        llm = ChatOpenAI(
            temperature=temperature,
            openai_api_base=cfg.ollama_api_base,
            openai_api_key=cfg.ollama_api_key,
            model_name="qwen3:235b",
        )
    elif model == "llama3.1":
        llm = ChatOpenAI(
            temperature=temperature,
            openai_api_base=cfg.llama_api_base,
            openai_api_key=cfg.llama_api_key,
            model_name="llama3.1:70b",
        )
    elif model == "yi":
        llm = ChatOpenAI(
            temperature=temperature,
            openai_api_base=cfg.ollama_api_base,
            openai_api_key=cfg.ollama_api_key,
            model_name="yi1.5:34b",
        )
    elif model == "claude-chat":
        llm = ChatOpenAI(
            temperature=0.7,
            openai_api_base=cfg.anthropic_api_base,
            openai_api_key=cfg.anthropic_api_key,
            model_name="claude-3-5-sonnet-20241022",
        )
    elif model == "deepseek":
        llm = ChatOpenAI(
            openai_api_base=cfg.anthropic_api_base,
            openai_api_key=cfg.anthropic_api_key,
            model_name="deepseek-chat",
        )
    else:
        return NotImplementedError
    return llm


logger.info("Start initializing LLMs...")

available_models = {
    "yi": get_llm(model="yi"),
    "hunyuan": get_llm(model="hunyuan"),
    "hunyuan-standard-256K": get_llm(model="hunyuan-standard-256K"),
    "claude-3-5-sonnet": get_llm(model="claude-3-5-sonnet"),
    "gpt-4o": get_llm(model="gpt-4o"),
    "baichuan4": get_llm(model="baichuan4"),
    "reflection-70b": get_llm(model="reflection-70b"),
    "qwen2-7b": get_llm(model="qwen2-7b"),
    "qwen2.5-72b": get_llm(model="qwen2.5-72b"),
    "llama3.1": get_llm(model="llama3.1"),
    "claude-chat": get_llm(model="claude-chat"),
    "deepseek": get_llm(model="deepseek"),
    "qwen3-235b-a22b:free": get_llm(model="qwen3-235b-a22b:free"),
    "qwen3-235b": get_llm(model="qwen3-235b"),
    "qwen3-32b:free": get_llm(model="qwen3-32b:free"),
    "deepseek-prove-v2:free": get_llm(model="deepseek-prover-v2:free"),
    "deepseek-v3-0324:free": get_llm(model="deepseek-chat-v3-0324:free"),
    "deepseek-r1:free": get_llm(model="deepseek-r1:free"),
}

LLM_DICT = {k: v for k, v in available_models.items() if v is not None}

if LLM_DICT:
    logger.info(f"Successfully initialized {len(LLM_DICT)} LLMs: {list(LLM_DICT.keys())}")
else:
    logger.warning("No LLMs were initialized. Please check your API configurations.")
