# /*
#  * @Author: yuymf
#  * @Date: 2025-08-25 15:52:53
#  * @Last Modified by:   yuymf
#  * @Last Modified time: 2025-08-25 15:52:53
#  */
from datetime import datetime, timedelta
from vups.config import read_vups_config


def get_zh_role_name(role_name, vup_dict: dict = None):

    vups = read_vups_config(vup_dict)

    # First check shortName
    for vup in vups:
        if vup["shortName"] == role_name:
            return role_name

    # Then check enName
    for vup in vups:
        if vup["enName"] == role_name:
            return vup["shortName"]

    # Finally check fullName
    for vup in vups:
        if vup["fullName"] == role_name:
            return vup["shortName"]

    return NotImplementedError

def get_vup_uid_by_short_name(short_name):
    """
    Get VUP UID by short name

    Args:
        short_name (str): Short name of the VUP

    Returns:
        int: VUP UID or None if not found
    """
    vup = get_vup_by_short_name(short_name)
    return vup["uid"] if vup else None

def get_vup_by_short_name(short_name):
    """
    Get VUP configuration by short name

    Args:
        short_name (str): Short name of the VUP

    Returns:
        dict: VUP configuration dictionary or None if not found
    """
    vups = read_vups_config()
    for vup in vups:
        if vup["shortName"] == short_name:
            return vup
    return None

def get_mid_with_role_name(role_name):

    # First try to get UID directly by any name (fullName, shortName, enName)
    uid = get_zh_role_name(role_name)
    if uid is not None:
        return str(uid)

    # Fallback to original logic for backward compatibility
    uid = get_vup_uid_by_short_name(role_name)
    if uid is None:
        raise Exception(f"Character {role_name} not found in vups.json")
    return str(uid)


def get_date_range(recent_days: int):
    end_date = datetime.now()
    start_date = end_date - timedelta(days=recent_days)

    start_date_str = start_date.strftime("%Y-%m-%d")
    end_date_str = end_date.strftime("%Y-%m-%d")

    return start_date_str, end_date_str
