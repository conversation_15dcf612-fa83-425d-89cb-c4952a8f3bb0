import unittest
from unittest.mock import AsyncMock, MagicMock, patch, call
from datetime import datetime

from langchain.schema import HumanMessage

from vups.base.action.relation_sum import RelationshipSummarize


class TestRelationshipSummarize(unittest.TestCase):
    def setUp(self):
        """Set up test fixtures before each test method."""
        # Mock the LLM_DICT to avoid actual LLM initialization
        with patch('vups.base.action.relation_sum.LLM_DICT') as mock_llm_dict:
            mock_llm = AsyncMock()
            mock_llm_dict.__getitem__.return_value = mock_llm
            self.relation_sum = RelationshipSummarize(char="xingtong", llm_name="claude-3-5-sonnet")
            self.mock_llm = mock_llm

    def test_init_default_values(self):
        """Test initialization with default values."""
        with patch('vups.base.action.relation_sum.LLM_DICT') as mock_llm_dict:
            mock_llm_dict.__getitem__.return_value = AsyncMock()

            relation_sum = RelationshipSummarize()

            self.assertEqual(relation_sum.char, "xingtong")
            self.assertEqual(relation_sum.token_max, 5000)
            mock_llm_dict.__getitem__.assert_called_with("claude-3-5-sonnet")

    def test_init_custom_values(self):
        """Test initialization with custom values."""
        with patch('vups.base.action.relation_sum.LLM_DICT') as mock_llm_dict:
            mock_llm_dict.__getitem__.return_value = AsyncMock()

            relation_sum = RelationshipSummarize(char="shanbao", llm_name="gpt-4o")

            self.assertEqual(relation_sum.char, "shanbao")
            mock_llm_dict.__getitem__.assert_called_with("gpt-4o")

    @patch('vups.base.action.relation_sum.U.get_zh_role_name')
    def test_init_char_zh_mapping(self, mock_get_zh_role_name):
        """Test that character name is properly mapped to Chinese name."""
        mock_get_zh_role_name.return_value = "星瞳"

        with patch('vups.base.action.relation_sum.LLM_DICT') as mock_llm_dict:
            mock_llm_dict.__getitem__.return_value = AsyncMock()

            relation_sum = RelationshipSummarize(char="xingtong")

            self.assertEqual(relation_sum.char_zh, "星瞳")
            mock_get_zh_role_name.assert_called_with("xingtong")

    @patch('vups.base.action.relation_sum.U.get_zh_role_name')
    def test_set_char(self, mock_get_zh_role_name):
        """Test set_char method updates both char and char_zh."""
        mock_get_zh_role_name.side_effect = ["星瞳", "扇宝"]

        with patch('vups.base.action.relation_sum.LLM_DICT') as mock_llm_dict:
            mock_llm_dict.__getitem__.return_value = AsyncMock()

            relation_sum = RelationshipSummarize(char="xingtong")
            relation_sum.set_char("shanbao")

            self.assertEqual(relation_sum.char, "shanbao")
            self.assertEqual(relation_sum.char_zh, "扇宝")
            mock_get_zh_role_name.assert_has_calls([call("xingtong"), call("shanbao")])

    @patch('vups.base.action.relation_sum.load_prompt')
    def test_render_rel_human_message(self, mock_load_prompt):
        """Test render_rel_human_message creates proper HumanMessage."""
        mock_load_prompt.return_value = "Test prompt for {char_zh}: {docs}"

        docs = "Test dynamic content"
        result = self.relation_sum.render_rel_human_message(docs)

        self.assertIsInstance(result, HumanMessage)
        mock_load_prompt.assert_called_with("relation_sum")
        # The exact content depends on char_zh which is mocked in setUp

    def test_parse_replies_empty_response(self):
        """Test parse_replies with '无' response."""
        result = self.relation_sum.parse_replies("无")
        self.assertEqual(result, [])

    def test_parse_replies_single_line(self):
        """Test parse_replies with single line response."""
        result = self.relation_sum.parse_replies("和星瞳一起玩游戏")
        self.assertEqual(result, ["和星瞳一起玩游戏"])

    def test_parse_replies_multiline(self):
        """Test parse_replies with multiline response."""
        replies = "和星瞳一起玩游戏\n祝贺扇宝生日快乐\n\n参加了联动活动"
        result = self.relation_sum.parse_replies(replies)
        expected = ["和星瞳一起玩游戏", "祝贺扇宝生日快乐", "参加了联动活动"]
        self.assertEqual(result, expected)

    def test_parse_replies_quote_replacement(self):
        """Test parse_replies replaces double quotes with single quotes."""
        result = self.relation_sum.parse_replies('和"星瞳"一起玩游戏')
        self.assertEqual(result, ["和'星瞳'一起玩游戏"])

    def test_parse_replies_filters_empty_lines(self):
        """Test parse_replies filters out empty lines."""
        replies = "和星瞳一起玩游戏\n\n\n祝贺扇宝生日快乐\n"
        result = self.relation_sum.parse_replies(replies)
        expected = ["和星瞳一起玩游戏", "祝贺扇宝生日快乐"]
        self.assertEqual(result, expected)


class TestRelationshipSummarizeAsync(unittest.IsolatedAsyncioTestCase):
    """Test class for async methods of RelationshipSummarize."""

    async def asyncSetUp(self):
        """Set up test fixtures before each async test method."""
        # Mock the LLM_DICT to avoid actual LLM initialization
        with patch('vups.base.action.relation_sum.LLM_DICT') as mock_llm_dict:
            self.mock_llm = AsyncMock()
            mock_llm_dict.__getitem__.return_value = self.mock_llm
            self.relation_sum = RelationshipSummarize(char="xingtong", llm_name="claude-3-5-sonnet")

    @patch('vups.base.action.relation_sum.get_connection')
    @patch('vups.base.action.relation_sum.U.get_mid_with_role_name')
    @patch('vups.base.action.relation_sum.U.get_date_range')
    async def test_relation_sum_success(self, mock_get_date_range, mock_get_mid, mock_get_connection):
        """Test successful relation_sum execution."""
        # Setup mocks
        mock_get_date_range.return_value = ("2025-01-01", "2025-01-07")
        mock_get_mid.return_value = "401315430"

        # Mock database connection and results
        mock_conn = AsyncMock()
        mock_get_connection.return_value.__aenter__.return_value = mock_conn
        mock_conn.fetch.return_value = [
            {"datetime": datetime(2025, 1, 1), "dynamic_content": "今天和朋友一起玩游戏"},
            {"datetime": datetime(2025, 1, 2), "dynamic_content": "祝贺扇宝生日快乐"},
            {"datetime": datetime(2025, 1, 3), "dynamic_content": None},  # Should be filtered out
        ]

        # Mock LLM response
        mock_response = MagicMock()
        mock_response.content = "和朋友一起玩游戏\n祝贺扇宝生日快乐"
        self.mock_llm.ainvoke.return_value = mock_response

        # Execute
        result = await self.relation_sum.relation_sum(7)

        # Verify
        self.assertEqual(result, "和朋友一起玩游戏\n祝贺扇宝生日快乐")
        mock_get_date_range.assert_called_once_with(7)
        mock_get_mid.assert_called_once()
        mock_conn.fetch.assert_called_once()
        self.mock_llm.ainvoke.assert_called_once()

    @patch('vups.base.action.relation_sum.get_connection')
    @patch('vups.base.action.relation_sum.U.get_mid_with_role_name')
    @patch('vups.base.action.relation_sum.U.get_date_range')
    async def test_relation_sum_with_datetime_objects(self, mock_get_date_range, mock_get_mid, mock_get_connection):
        """Test relation_sum when get_date_range returns datetime objects."""
        # Setup mocks with datetime objects instead of strings
        start_time = datetime(2025, 1, 1)
        end_time = datetime(2025, 1, 7)
        mock_get_date_range.return_value = (start_time, end_time)
        mock_get_mid.return_value = "401315430"

        # Mock database connection
        mock_conn = AsyncMock()
        mock_get_connection.return_value.__aenter__.return_value = mock_conn
        mock_conn.fetch.return_value = []

        # Mock LLM response
        mock_response = MagicMock()
        mock_response.content = "无"
        self.mock_llm.ainvoke.return_value = mock_response

        # Execute
        result = await self.relation_sum.relation_sum(7)

        # Verify datetime objects are used directly
        self.assertEqual(result, "无")
        mock_conn.fetch.assert_called_once()
        call_args = mock_conn.fetch.call_args[0]
        self.assertEqual(call_args[1], "401315430")  # mid
        self.assertEqual(call_args[2], start_time)   # start_time
        self.assertEqual(call_args[3], end_time)     # end_time

    @patch('vups.base.action.relation_sum.get_connection')
    @patch('vups.base.action.relation_sum.U.get_mid_with_role_name')
    @patch('vups.base.action.relation_sum.U.get_date_range')
    async def test_relation_sum_with_string_dates(self, mock_get_date_range, mock_get_mid, mock_get_connection):
        """Test relation_sum when get_date_range returns string dates."""
        # Setup mocks with string dates
        mock_get_date_range.return_value = ("2025-01-01", "2025-01-07")
        mock_get_mid.return_value = "401315430"

        # Mock database connection
        mock_conn = AsyncMock()
        mock_get_connection.return_value.__aenter__.return_value = mock_conn
        mock_conn.fetch.return_value = []

        # Mock LLM response
        mock_response = MagicMock()
        mock_response.content = "无"
        self.mock_llm.ainvoke.return_value = mock_response

        # Execute
        result = await self.relation_sum.relation_sum(7)

        # Verify string dates are converted to datetime objects
        self.assertEqual(result, "无")
        mock_conn.fetch.assert_called_once()
        call_args = mock_conn.fetch.call_args[0]
        self.assertEqual(call_args[1], "401315430")  # mid
        self.assertIsInstance(call_args[2], datetime)  # start_time converted
        self.assertIsInstance(call_args[3], datetime)  # end_time converted

    @patch('vups.base.action.relation_sum.get_connection')
    @patch('vups.base.action.relation_sum.U.get_mid_with_role_name')
    @patch('vups.base.action.relation_sum.U.get_date_range')
    async def test_relation_sum_empty_results(self, mock_get_date_range, mock_get_mid, mock_get_connection):
        """Test relation_sum with empty database results."""
        # Setup mocks
        mock_get_date_range.return_value = ("2025-01-01", "2025-01-07")
        mock_get_mid.return_value = "401315430"

        # Mock database connection with empty results
        mock_conn = AsyncMock()
        mock_get_connection.return_value.__aenter__.return_value = mock_conn
        mock_conn.fetch.return_value = []

        # Mock LLM response
        mock_response = MagicMock()
        mock_response.content = "无"
        self.mock_llm.ainvoke.return_value = mock_response

        # Execute
        result = await self.relation_sum.relation_sum(7)

        # Verify
        self.assertEqual(result, "无")
        # Verify that empty doc string is passed to LLM
        self.mock_llm.ainvoke.assert_called_once()

    @patch('vups.base.action.relation_sum.get_connection')
    @patch('vups.base.action.relation_sum.U.get_mid_with_role_name')
    @patch('vups.base.action.relation_sum.U.get_date_range')
    async def test_relation_sum_filters_null_content(self, mock_get_date_range, mock_get_mid, mock_get_connection):
        """Test relation_sum filters out rows with null dynamic_content."""
        # Setup mocks
        mock_get_date_range.return_value = ("2025-01-01", "2025-01-07")
        mock_get_mid.return_value = "401315430"

        # Mock database connection with mixed null and valid content
        mock_conn = AsyncMock()
        mock_get_connection.return_value.__aenter__.return_value = mock_conn
        mock_conn.fetch.return_value = [
            {"datetime": datetime(2025, 1, 1), "dynamic_content": "有效内容1"},
            {"datetime": datetime(2025, 1, 2), "dynamic_content": None},
            {"datetime": datetime(2025, 1, 3), "dynamic_content": ""},  # Empty string should be filtered
            {"datetime": datetime(2025, 1, 4), "dynamic_content": "有效内容2"},
        ]

        # Mock LLM response
        mock_response = MagicMock()
        mock_response.content = "测试响应"
        self.mock_llm.ainvoke.return_value = mock_response

        # Execute
        result = await self.relation_sum.relation_sum(7)

        # Verify only valid content is included
        self.assertEqual(result, "测试响应")
        # Check that the doc passed to LLM contains only valid content
        call_args = self.mock_llm.ainvoke.call_args[0][0]
        self.assertIsInstance(call_args[0], HumanMessage)

    @patch('vups.base.action.relation_sum.get_connection')
    @patch('vups.base.action.relation_sum.U.get_mid_with_role_name')
    @patch('vups.base.action.relation_sum.U.get_date_range')
    @patch('vups.base.action.relation_sum.logger')
    async def test_relation_sum_llm_error(self, mock_logger, mock_get_date_range, mock_get_mid, mock_get_connection):
        """Test relation_sum handles LLM errors gracefully."""
        # Setup mocks
        mock_get_date_range.return_value = ("2025-01-01", "2025-01-07")
        mock_get_mid.return_value = "401315430"

        # Mock database connection
        mock_conn = AsyncMock()
        mock_get_connection.return_value.__aenter__.return_value = mock_conn
        mock_conn.fetch.return_value = [
            {"datetime": datetime(2025, 1, 1), "dynamic_content": "测试内容"}
        ]

        # Mock LLM to raise an exception
        self.mock_llm.ainvoke.side_effect = Exception("LLM API Error")

        # Execute
        result = await self.relation_sum.relation_sum(7)

        # Verify error handling
        self.assertEqual(result, "LLM可能缺少额度，请联系管理员")
        mock_logger.error.assert_called_once()
        error_call = mock_logger.error.call_args[0][0]
        self.assertIn("LLM API Error", error_call)

    @patch('vups.base.action.relation_sum.get_connection')
    @patch('vups.base.action.relation_sum.U.get_mid_with_role_name')
    @patch('vups.base.action.relation_sum.U.get_date_range')
    async def test_relation_sum_recent_days_edge_cases(self, mock_get_date_range, mock_get_mid, mock_get_connection):
        """Test relation_sum with edge case values for recent_days."""
        # Setup mocks
        mock_get_mid.return_value = "401315430"
        mock_conn = AsyncMock()
        mock_get_connection.return_value.__aenter__.return_value = mock_conn
        mock_conn.fetch.return_value = []

        mock_response = MagicMock()
        mock_response.content = "无"
        self.mock_llm.ainvoke.return_value = mock_response

        # Test with 0 days
        mock_get_date_range.return_value = ("2025-01-01", "2025-01-01")
        result = await self.relation_sum.relation_sum(0)
        self.assertEqual(result, "无")
        mock_get_date_range.assert_called_with(0)

        # Test with 1 day
        mock_get_date_range.return_value = ("2025-01-01", "2025-01-02")
        result = await self.relation_sum.relation_sum(1)
        self.assertEqual(result, "无")
        mock_get_date_range.assert_called_with(1)

        # Test with large number of days
        mock_get_date_range.return_value = ("2023-01-01", "2025-01-01")
        result = await self.relation_sum.relation_sum(365)
        self.assertEqual(result, "无")
        mock_get_date_range.assert_called_with(365)

    @patch('vups.base.action.relation_sum.logger')
    async def test_run_method_success(self, mock_logger):
        """Test the main run method with successful execution."""
        # Mock the relation_sum method
        with patch.object(self.relation_sum, 'relation_sum') as mock_relation_sum:
            mock_relation_sum.return_value = "和星瞳一起玩游戏\n祝贺扇宝生日快乐"

            # Execute
            result = await self.relation_sum.run(7)

            # Verify
            expected = ["和星瞳一起玩游戏", "祝贺扇宝生日快乐"]
            self.assertEqual(result, expected)
            mock_relation_sum.assert_called_once_with(7)
            mock_logger.info.assert_called_once()

    async def test_run_method_with_args_kwargs(self):
        """Test the run method ignores additional args and kwargs."""
        # Mock the relation_sum method
        with patch.object(self.relation_sum, 'relation_sum') as mock_relation_sum:
            mock_relation_sum.return_value = "无"

            # Execute with additional args and kwargs
            result = await self.relation_sum.run(7, "extra_arg", extra_kwarg="value")

            # Verify
            self.assertEqual(result, [])
            mock_relation_sum.assert_called_once_with(7)

    async def test_run_method_empty_response(self):
        """Test the run method with empty LLM response."""
        # Mock the relation_sum method
        with patch.object(self.relation_sum, 'relation_sum') as mock_relation_sum:
            mock_relation_sum.return_value = "无"

            # Execute
            result = await self.relation_sum.run(7)

            # Verify
            self.assertEqual(result, [])
            mock_relation_sum.assert_called_once_with(7)

    async def test_run_method_propagates_exceptions(self):
        """Test the run method propagates exceptions from relation_sum."""
        # Mock the relation_sum method to raise an exception
        with patch.object(self.relation_sum, 'relation_sum') as mock_relation_sum:
            mock_relation_sum.side_effect = Exception("Database connection failed")

            # Execute and verify exception is propagated
            with self.assertRaises(Exception) as context:
                await self.relation_sum.run(7)

            self.assertIn("Database connection failed", str(context.exception))

    @patch('vups.base.action.relation_sum.get_connection')
    @patch('vups.base.action.relation_sum.U.get_mid_with_role_name')
    @patch('vups.base.action.relation_sum.U.get_date_range')
    async def test_database_query_parameters(self, mock_get_date_range, mock_get_mid, mock_get_connection):
        """Test that correct SQL query and parameters are used."""
        # Setup mocks
        mock_get_date_range.return_value = ("2025-01-01", "2025-01-07")
        mock_get_mid.return_value = "401315430"

        # Mock database connection
        mock_conn = AsyncMock()
        mock_get_connection.return_value.__aenter__.return_value = mock_conn
        mock_conn.fetch.return_value = []

        # Mock LLM response
        mock_response = MagicMock()
        mock_response.content = "无"
        self.mock_llm.ainvoke.return_value = mock_response

        # Execute
        await self.relation_sum.relation_sum(7)

        # Verify SQL query and parameters
        mock_conn.fetch.assert_called_once()
        call_args = mock_conn.fetch.call_args

        # Check SQL query structure
        sql_query = call_args[0][0]
        self.assertIn("SELECT datetime, dynamic_content", sql_query)
        self.assertIn("FROM dynamics_table", sql_query)
        self.assertIn("WHERE uid = $1", sql_query)
        self.assertIn("AND datetime BETWEEN $2 AND $3", sql_query)
        self.assertIn("AND dynamic_content IS NOT NULL", sql_query)
        self.assertIn("ORDER BY datetime", sql_query)

        # Check parameters
        self.assertEqual(call_args[0][1], "401315430")  # uid parameter
        self.assertIsInstance(call_args[0][2], datetime)  # start_time parameter
        self.assertIsInstance(call_args[0][3], datetime)  # end_time parameter


if __name__ == '__main__':
    unittest.main()
