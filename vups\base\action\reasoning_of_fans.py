import re
import json

from langchain.schema import HumanMessage, SystemMessage

from vups.base.action.abstract_action import Action
from vups.algos.prompts import load_prompt
import vups.utils as U
from vups.logger import logger
from vups.base.schema.data_class import VupRencentInfo


class ReasoningOfFans(Action):
    def __init__(
        self,
        char: str = "xingtong",
        llm_name="claude-3-5-sonnet",
    ):
        super().__init__(llm_name, "reasoning_of_fans")
        self.token_max = 5000

        self.char = char
        self.set_char(self.char)

    def set_char(self, char):
        self.char = char
        self.char_zh = U.get_zh_role_name(self.char)

    def render_rel_human_message(self, recent_info: VupRencentInfo):
        if recent_info is None:
            raise ValueError("recent_info is None")
        if U.safe_int(recent_info.follower_change) > 0:
            follower_change = f"新增{recent_info.follower_change}粉丝"
        else:
            follower_change = f"减少{abs(recent_info.follower_change)}粉丝"

        if recent_info.video_content == []:
            video_content = "没有找到最近的视频"
        else:
            video_content = (
                f"最近在{recent_info.video_content[0]}时,发布了视频⌈{recent_info.video_content[1]}⌋，获得了{recent_info.video_content[2]}播放量，并获得了{recent_info.video_content[3]}的荣誉"
                if recent_info.video_content[3] != ""
                else f"最近在{recent_info.video_content[0]}时,发布了视频⌈{recent_info.video_content[1]}⌋，获得了{recent_info.video_content[2]}播放量"
            )

        if recent_info.dynamic_content == []:
            dynamic_content = "没有找到最近的动态"
        else:
            context = re.sub(r"\[.*?\]", "", recent_info.dynamic_content[1])
            dynamic_content = (
                f"最近在{recent_info.dynamic_content[0]}时,发布了动态⌈{context}⌋"
            )

        if recent_info.live_content == "":
            live_content = "最近没有直播"
        else:
            live_content = f"最近,进行了直播⌈{recent_info.live_content}⌋"

        if recent_info.relations == []:
            relation = "最近没有联动活动"
        else:
            relation = " ".join(recent_info.relations)

        if recent_info.rise_videos == []:
            rise_video_content = "没有找到最近的视频增长情况"
        else:
            rise_video_content = "目前视频日增前三为:"
            for rise in recent_info.rise_videos:
                rise_video_content += (
                    f"⌈{rise[2]}⌋, 目前总播放为{rise[3]}, 日播放量增长为{rise[4]}"
                )

        if recent_info.tieba_topic == []:
            tieba_content = "没有找到贴吧最近在聊内容"
        else:
            tieba_content = "".join(
                [json.dumps(d, ensure_ascii=False) for d in recent_info.tieba_topic]
            )

        prompt = f"""Think step by step. The following pieces are recent activities of the vtuber {self.char_zh}:

<Activities>
**时间**：{recent_info.time}
**事件**：{follower_change}
**视频**：{video_content}
**动态**：{dynamic_content}
**直播**：{live_content}
**联动情况**：{relation}
**视频播放量增长情况**：{rise_video_content}
**贴吧最近在聊**：
{tieba_content}
</Activities>

Summarize the reasons for the change in the number of fans of vtuber {self.char_zh} in the past days.
"""
        return HumanMessage(content=prompt)

    def render_rel_system_message(self):
        prompt = load_prompt("reasoning_of_fans") # TODO: Web Search
        return SystemMessage(content=prompt)

    async def reasoning_sum(self, recent_info: VupRencentInfo):
        system_message = self.render_rel_system_message()
        human_msg = self.render_rel_human_message(recent_info)
        logger.info(f"****Summerize Recent Info Human Message Input****: {human_msg}")
        try:
            response = await self.llm.ainvoke([system_message, human_msg])
            res = response.content
            res = res.replace("\n\n", "\n")
            logger.info(f"****Summerize Recent Info LLM Response****: {res}")
        except Exception as e:
            logger.error(f"****Summerize Recent Info LLM Error****: {e}")
            res = "LLM可能缺少额度，请联系管理员"
        return res

    async def run(self, recent_info: VupRencentInfo, *args, **kwargs):
        logger.info(f"run {self.__repr__()}")
        res = await self.reasoning_sum(recent_info)
        return res
