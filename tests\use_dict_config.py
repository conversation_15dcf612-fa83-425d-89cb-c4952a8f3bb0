from vups.base.llm import get_llm
from vups.base.llm.config import LLMConfig

def main():
    config_dict = {
        "hunyuan_api_base": "https://your-hunyuan-api-base",
        "hunyuan_api_key": "your-hunyuan-api-key",
        "anthropic_api_base": "https://your-anthropic-api-base",
        "anthropic_api_key": "your-anthropic-api-key",
        "baichuan_api_base": "https://api.baichuan-ai.com/v1/chat/completions",
        "baichuan_api_key": "your-baichuan-api-key"
    }

    config = LLMConfig.from_dict(config_dict)

    hunyuan_llm = get_llm(model="hunyuan-standard-256K", config=config)
    claude_llm = get_llm(model="claude-3-opus", config=config)

    response = hunyuan_llm.invoke("你好！")
    print(response)

    response = claude_llm.invoke("Hello!")
    print(response)

if __name__ == "__main__":
    main()
