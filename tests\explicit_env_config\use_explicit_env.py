from vups.base.llm import get_llm
from vups.base.llm.config import LLMConfig

def main():
    config = LLMConfig.from_env()

    hunyuan_llm = get_llm(model="hunyuan-standard-256K", config=config)
    claude_llm = get_llm(model="claude-3-opus", config=config)

    response = hunyuan_llm.invoke("你好！")
    print(response)

    response = claude_llm.invoke("Hello!")
    print(response)

if __name__ == "__main__":
    main()
